## CDN工作原理与优化

### CDN基本概念
CDN（内容分发网络）通过全球边缘节点缓存内容，减少网络延迟，提高访问速度和用户体验。

**核心价值**：
- 减少网络延迟和带宽压力
- 提高可用性和容灾能力
- 改善用户体验

### CDN工作流程

#### 基本架构
```
用户 → 本地DNS → CDN DNS → 边缘节点 → 源站
```

#### 请求处理流程
1. **DNS解析**：将域名解析到最优CDN节点
2. **缓存检查**：节点检查内容是否已缓存
3. **缓存命中**：直接返回缓存内容
4. **缓存未命中**：回源获取内容，缓存后返回

#### 智能调度策略
- **地理位置**：选择距离最近的节点
- **网络状况**：考虑延迟和丢包率
- **节点负载**：避免过载节点
- **内容可用性**：确保内容已缓存

### CDN缓存策略

#### 缓存层次架构
| 层次 | 位置 | 特点 | 作用 |
|------|------|------|------|
| **L1缓存** | 边缘节点 | 最接近用户，容量小 | 提供最快响应 |
| **L2缓存** | 区域节点 | 覆盖更大区域，容量大 | L1的上游缓存 |
| **源站** | 原始服务器 | 最终数据源 | 内容的权威来源 |

#### 缓存淘汰算法
- **LRU**：淘汰最久未使用，适合访问模式稳定
- **LFU**：淘汰访问频率最低，适合热点内容明显
- **TTL**：基于时间过期，适合更新频率已知

#### 缓存控制策略
- **强缓存**：`Cache-Control: max-age=3600`，直接使用缓存
- **协商缓存**：`ETag`、`Last-Modified`，向源站确认更新
- **不缓存**：`Cache-Control: no-cache`，每次请求源站

### CDN应用类型

| 类型 | 适用内容 | 特点 | 关键技术 |
|------|----------|------|----------|
| **静态内容CDN** | 图片、CSS、JS、视频 | 变化少、缓存时间长、命中率高 | 长期缓存、版本控制 |
| **动态内容CDN** | API响应、个性化内容 | 实时性要求高 | 边缘计算、智能路由 |
| **流媒体CDN** | 视频直播、点播 | 流式传输、自适应码率 | HLS/DASH、边缘转码 |

### CDN性能优化策略

#### 缓存优化
- **提高命中率**：合理设置TTL、预热热点内容、优化缓存键
- **减少回源**：增加缓存容量、多层架构、智能预取

#### 网络优化
- **协议优化**：HTTP/2、QUIC协议、TCP优化
- **压缩优化**：Gzip/Brotli压缩、图片格式优化

#### 边缘计算
- **功能**：边缘函数执行、内容动态生成、请求处理
- **优势**：减少延迟、降低源站压力、提高响应速度

### CDN安全与监控

#### 安全防护
- **DDoS防护**：流量清洗、黑白名单、多节点分散攻击
- **访问控制**：地理位置限制、防盗链、Token验证
- **内容安全**：HTTPS支持、恶意内容检测、合规性检查

#### 关键监控指标
| 类型 | 指标 | 说明 |
|------|------|------|
| **性能指标** | 缓存命中率、TTFB、响应时间 | 衡量CDN效果 |
| **业务指标** | 带宽使用、请求量、热点分析 | 业务运营数据 |
| **成本指标** | 流量成本、存储成本、回源成本 | 成本控制 |

### 面试要点

**Q: CDN如何提高网站性能？**
A:
1. **减少延迟**：就近访问边缘节点
2. **减少带宽**：缓存减少重复传输
3. **分散负载**：减轻源站压力
4. **提高可用性**：多节点容灾

**Q: CDN缓存更新策略有哪些？**
A:
- **主动刷新**：手动清除缓存
- **被动更新**：TTL过期自动更新
- **版本控制**：URL添加版本号强制更新
- **智能更新**：基于内容变化自动检测

**Q: 如何选择CDN服务商？**
A:
- **节点覆盖**：是否覆盖目标用户区域
- **性能表现**：延迟、稳定性、命中率
- **功能特性**：是否满足业务需求
- **成本模式**：流量计费、带宽计费
- **技术支持**：服务质量和响应速度

**Q: CDN有哪些局限性？**
A:
- **首次访问延迟**：仍需回源获取内容
- **动态内容限制**：个性化内容缓存效果有限
- **成本考虑**：大流量时费用较高
- **复杂性增加**：增加系统架构复杂度
