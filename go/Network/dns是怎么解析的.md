## DNS域名解析过程

### DNS解析流程

DNS（Domain Name System）将域名转换为IP地址的分布式系统。

#### 完整解析流程
```
用户输入域名 www.example.com
    ↓
1. 浏览器缓存查询
    ↓ (未命中)
2. 操作系统缓存查询
    ↓ (未命中)
3. 本地DNS服务器查询
    ↓ (未命中)
4. 根域名服务器查询 → 返回.com顶级域服务器地址
    ↓
5. 顶级域服务器查询 → 返回example.com权威服务器地址
    ↓
6. 权威DNS服务器查询 → 返回www.example.com的IP地址
    ↓
7. 逐级返回结果并缓存
    ↓
8. 浏览器获得IP地址，建立连接
```

### DNS缓存层级

#### 缓存优先级（从高到低）
1. **浏览器缓存**：浏览器内部DNS缓存
2. **操作系统缓存**：系统级DNS缓存
3. **路由器缓存**：本地网络设备缓存
4. **ISP DNS缓存**：运营商DNS服务器缓存

#### 缓存查询示例
```
查询 www.baidu.com:
1. Chrome浏览器缓存 → 命中，返回IP
2. 如果未命中 → 查询系统缓存
3. 如果未命中 → 查询路由器
4. 如果未命中 → 查询ISP DNS服务器
```

### DNS服务器层次结构

#### 服务器类型
| 服务器类型 | 作用 | 示例 |
|------------|------|------|
| **根域名服务器** | 顶级域指向 | 全球13组根服务器 |
| **顶级域服务器** | 二级域指向 | .com, .org, .cn |
| **权威DNS服务器** | 域名最终解析 | example.com的NS服务器 |
| **递归DNS服务器** | 代理查询 | *******, *************** |

#### 查询示例：www.example.com
```
1. 根服务器: "查询.com域，去找.com顶级域服务器"
2. .com服务器: "查询example.com，去找example.com权威服务器"
3. 权威服务器: "www.example.com的IP是*************"
```

### DNS记录类型

| 记录类型 | 作用 | 示例 |
|----------|------|------|
| **A** | 域名→IPv4地址 | www.example.com → ************* |
| **AAAA** | 域名→IPv6地址 | www.example.com → 2606:2800:220:1:248:1893:25c8:1946 |
| **CNAME** | 域名别名 | blog.example.com → www.example.com |
| **MX** | 邮件服务器 | example.com → mail.example.com |
| **NS** | 域名服务器 | example.com → ns1.example.com |
| **TXT** | 文本记录 | 用于验证、SPF等 |

### 查询类型

#### 递归查询
- **特点**：DNS服务器负责完整查询过程
- **流程**：客户端 → 递归服务器 → 最终结果
- **优点**：客户端简单，只需一次查询

#### 迭代查询
- **特点**：客户端自己完成查询过程
- **流程**：客户端依次查询各级服务器
- **优点**：减轻DNS服务器负担

### DNS性能优化

#### TTL（生存时间）
```
A记录: www.example.com 300 IN A *************
       ↑
    TTL=300秒，缓存5分钟
```

#### 常见TTL设置
- **频繁变更的记录**：300秒（5分钟）
- **稳定的记录**：3600秒（1小时）
- **CDN记录**：60秒（1分钟）

#### DNS预解析
- HTML中使用`<link rel="dns-prefetch">`标签
- 提前解析可能用到的域名
- 减少后续请求的DNS解析时间

### DNS安全

#### 常见攻击
1. **DNS劫持**：篡改DNS响应
2. **DNS缓存投毒**：污染DNS缓存
3. **DNS放大攻击**：利用DNS进行DDoS

#### 安全措施
1. **DNSSEC**：DNS安全扩展，数字签名验证
2. **DoH/DoT**：DNS over HTTPS/TLS，加密传输
3. **可信DNS**：使用可靠的DNS服务商

### 常用DNS服务器

#### 公共DNS服务器
| 服务商 | IPv4 | IPv6 | 特点 |
|--------|------|------|------|
| **Google** | *******, ******* | 2001:4860:4860::8888 | 快速、稳定 |
| **Cloudflare** | *******, ******* | 2606:4700:4700::1111 | 隐私保护 |
| **阿里** | *********, ********* | 2400:3200::1 | 国内优化 |
| **腾讯** | ************ | 2402:4e00:: | 国内优化 |

### Go语言DNS操作

#### 常用DNS查询函数
- `net.LookupIP()`：查询A/AAAA记录
- `net.LookupCNAME()`：查询CNAME记录
- `net.LookupMX()`：查询MX记录
- `net.LookupTXT()`：查询TXT记录

### 面试要点

**Q: DNS解析的完整过程？**
A:
1. 检查浏览器缓存
2. 检查操作系统缓存
3. 查询本地DNS服务器
4. 递归查询：根服务器→顶级域服务器→权威服务器
5. 返回结果并缓存

**Q: 递归查询和迭代查询的区别？**
A:
- **递归查询**：DNS服务器代为完成所有查询，返回最终结果
- **迭代查询**：DNS服务器只返回下一步查询地址，客户端自己查询

**Q: 如何优化DNS解析性能？**
A:
1. **合理设置TTL**：平衡缓存效果和更新及时性
2. **DNS预解析**：提前解析可能用到的域名
3. **使用CDN**：就近访问，减少解析时间
4. **选择快速DNS服务器**：如*******、*******

**Q: DNS缓存的作用和问题？**
A:
- **作用**：提高解析速度，减少网络请求
- **问题**：可能返回过期数据，需要合理设置TTL

**Q: DNSSEC的作用？**
A: 通过数字签名验证DNS响应的真实性和完整性，防止DNS劫持和缓存投毒攻击