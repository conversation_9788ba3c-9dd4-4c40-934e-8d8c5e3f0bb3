## SYN Flood攻击详解

### 攻击原理

SYN Flood是一种利用TCP三次握手机制的DDoS攻击，通过大量伪造的SYN请求耗尽服务器连接资源。

#### TCP三次握手正常流程
```
客户端                    服务器
  |                        |
  |-------- SYN ---------->| 1. 请求连接
  |<----- SYN+ACK ---------| 2. 确认并分配资源
  |-------- ACK ---------->| 3. 连接建立完成
  |                        |
  |     正常数据传输        |
```

#### SYN Flood攻击流程
```
攻击者                    服务器
  |                        |
  |-------- SYN ---------->| 1. 伪造源IP发送SYN
  |<----- SYN+ACK ---------| 2. 分配资源，进入SYN_RCVD状态
  |        (无响应)         | 3. 攻击者不回复ACK
  |-------- SYN ---------->| 4. 继续发送大量SYN请求
  |<----- SYN+ACK ---------| 5. 半连接队列逐渐填满
  |                        | 6. 服务器资源耗尽，拒绝新连接
```

### 攻击特征与影响

#### 系统状态变化
- **半连接堆积**：大量连接处于SYN_RCVD状态
- **资源消耗**：每个半连接占用内存和端口资源
- **队列溢出**：半连接队列达到上限，新连接被拒绝
- **性能下降**：CPU处理大量SYN包，系统负载升高

#### 攻击识别特征
| 监控指标 | 正常值 | 攻击时 | 说明 |
|----------|--------|--------|------|
| **SYN_RCVD连接数** | <100 | >1000 | 半连接状态异常增多 |
| **连接成功率** | >99% | <50% | 大量连接建立失败 |
| **SYN包比例** | <10% | >50% | SYN包占总流量比例异常 |
| **响应时间** | <100ms | >5s | 服务响应严重延迟 |

### 核心防护技术

#### 1. SYN Cookies机制
**工作原理**：
```
传统方式：SYN → 分配资源 → SYN+ACK → 等待ACK
SYN Cookies：SYN → 生成Cookie → SYN+ACK(含Cookie) → 验证ACK → 分配资源
```

**技术优势**：
- **无状态**：不为半连接分配资源
- **透明性**：对正常连接无影响
- **高效性**：有效防止资源耗尽

**Cookie生成算法**：
- 基于源IP、端口、时间戳等信息
- 使用加密哈希确保安全性
- 包含连接参数信息

#### 2. 系统层面优化
**关键参数调优**：
```bash
# 启用SYN Cookies
net.ipv4.tcp_syncookies = 1

# 调整队列大小
net.ipv4.tcp_max_syn_backlog = 2048
net.core.somaxconn = 1024

# 优化超时设置
net.ipv4.tcp_syn_retries = 3
net.ipv4.tcp_synack_retries = 2
```

#### 3. 网络层防护
**防火墙策略**：
- **频率限制**：限制单IP的SYN包频率
- **连接数限制**：限制单IP的并发连接数
- **地理位置过滤**：阻止异常地区的连接

**负载均衡**：
- **流量分散**：多服务器分担攻击压力
- **健康检查**：自动剔除异常服务器
- **弹性扩容**：根据负载动态调整资源

### 攻击检测与监控

#### 1. 实时监控命令
```bash
# 查看半连接数量
netstat -an | grep SYN_RECV | wc -l

# 连接状态统计
ss -ant | awk '{print $1}' | sort | uniq -c

# 监控SYN包流量
tcpdump -i eth0 'tcp[tcpflags] & tcp-syn != 0' -c 100

# 查看网络连接统计
cat /proc/net/netstat | grep TcpExt
```

#### 2. 关键监控指标
| 指标类型 | 监控项 | 告警阈值 | 说明 |
|----------|--------|----------|------|
| **连接状态** | SYN_RECV数量 | >500 | 半连接异常增多 |
| **成功率** | 连接建立成功率 | <90% | 连接失败率过高 |
| **性能** | 平均响应时间 | >1s | 服务响应延迟 |
| **资源** | CPU/内存使用率 | >80% | 系统资源紧张 |

### 应用层防护实现

#### Go语言连接限制
```go
type ConnectionLimiter struct {
    connections map[string]int
    mutex       sync.RWMutex
    maxConn     int
}

func (cl *ConnectionLimiter) Allow(ip string) bool {
    cl.mutex.Lock()
    defer cl.mutex.Unlock()

    if cl.connections[ip] >= cl.maxConn {
        return false
    }
    cl.connections[ip]++
    return true
}
```

#### 速率限制实现
```go
import "golang.org/x/time/rate"

type RateLimiter struct {
    limiters map[string]*rate.Limiter
    mutex    sync.RWMutex
}

func (rl *RateLimiter) Allow(ip string) bool {
    rl.mutex.Lock()
    defer rl.mutex.Unlock()

    limiter, exists := rl.limiters[ip]
    if !exists {
        limiter = rate.NewLimiter(rate.Every(time.Second), 10)
        rl.limiters[ip] = limiter
    }
    return limiter.Allow()
}
```

### 面试要点

**Q: SYN Flood攻击的核心原理？**
A:
1. 利用TCP三次握手机制缺陷
2. 伪造大量源IP发送SYN包
3. 服务器分配资源等待ACK回复
4. 攻击者不回复，导致半连接堆积
5. 服务器资源耗尽，拒绝新连接

**Q: SYN Cookies如何解决SYN Flood？**
A:
1. **无状态设计**：收到SYN时不分配资源
2. **Cookie生成**：基于连接信息生成加密cookie
3. **延迟分配**：收到有效ACK后再分配资源
4. **透明防护**：对正常连接无影响

**Q: 如何检测和监控SYN Flood攻击？**
A:
1. **连接状态监控**：SYN_RECV状态连接数异常
2. **性能指标**：连接成功率下降、响应时间增加
3. **流量分析**：SYN包占比异常增高
4. **系统资源**：CPU、内存使用率飙升

**Q: 除了SYN Cookies还有哪些防护方法？**
A:
1. **系统调优**：调整队列大小、超时参数
2. **防火墙规则**：限制SYN包频率和连接数
3. **负载均衡**：分散攻击压力
4. **流量清洗**：CDN和DDoS防护服务