## 跨域问题与解决方案

### 跨域基本概念

**同源策略**：浏览器安全机制，限制不同源之间的资源访问

**同源条件**：协议、域名、端口完全相同
- `https://www.example.com:8080` 与 `https://api.example.com:8080` → 跨域（子域名不同）
- `https://www.example.com:8080` 与 `http://www.example.com:8080` → 跨域（协议不同）
- `https://www.example.com:8080` 与 `https://www.example.com:3000` → 跨域（端口不同）

### 跨域限制范围

**受限制**：
- Ajax请求（XMLHttpRequest、Fetch）
- DOM操作
- Cookie、LocalStorage读取
- Canvas图像数据

**不受限制**：
- 静态资源加载（img、link、script）
- 表单提交
- iframe嵌入（部分限制）

### 主要解决方案

#### 1. CORS（跨域资源共享）

**简单请求**：满足特定条件，直接发送
- 方法：GET、POST、HEAD
- 头部：Accept、Accept-Language、Content-Language、Content-Type（限定值）

**预检请求**：不满足简单请求条件，先发OPTIONS
- 自定义头部
- PUT、DELETE等方法
- 特殊Content-Type

**关键响应头**：
- `Access-Control-Allow-Origin`：允许的源
- `Access-Control-Allow-Methods`：允许的方法
- `Access-Control-Allow-Headers`：允许的头部
- `Access-Control-Allow-Credentials`：是否允许凭证
- `Access-Control-Max-Age`：预检缓存时间

#### 2. JSONP
**原理**：利用script标签不受同源策略限制
**限制**：只支持GET请求，安全性较低
**适用**：老浏览器兼容，简单数据获取

#### 3. 代理服务器
**开发环境**：webpack-dev-server代理
**生产环境**：Nginx反向代理
**优点**：完全绕过浏览器限制，支持所有HTTP方法

#### 4. PostMessage
**适用场景**：iframe、window间通信
**安全性**：需验证origin来源

#### 5. WebSocket
**特点**：不受同源策略限制
**适用**：实时通信场景

### 服务端CORS配置

#### Go语言实现
```go
func CORSMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        c.Header("Access-Control-Allow-Origin", "https://trusted.com")
        c.Header("Access-Control-Allow-Credentials", "true")
        c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE")
        c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")

        if c.Request.Method == "OPTIONS" {
            c.AbortWithStatus(204)
            return
        }
        c.Next()
    }
}
```

### 安全配置要点

#### 1. Origin白名单
**避免通配符**：`Access-Control-Allow-Origin: *` 配合 `credentials: true` 不安全
**动态设置**：根据请求Origin动态设置允许的源

#### 2. 凭证处理
**Cookie跨域**：
- 客户端：`credentials: 'include'`
- 服务端：`Access-Control-Allow-Credentials: true`
- 限制：不能使用通配符Origin

#### 3. 预检优化
**缓存设置**：`Access-Control-Max-Age: 86400`
**减少预检**：使用简单请求条件

### 常见问题

1. **预检请求失败**：确保服务器支持OPTIONS方法，正确设置CORS头部
2. **Cookie无法携带**：需要同时设置客户端`credentials`和服务端`Allow-Credentials`
3. **自定义头部被拒绝**：在`Access-Control-Allow-Headers`中添加自定义头部

### 面试要点

**Q: 什么是跨域？如何解决？**
A:
- **定义**：协议、域名、端口任一不同导致的浏览器限制
- **解决方案**：CORS、JSONP、代理、PostMessage、WebSocket

**Q: CORS简单请求和预检请求的区别？**
A:
- **简单请求**：满足特定条件（GET/POST/HEAD，标准头部），直接发送
- **预检请求**：不满足条件时，先发OPTIONS请求确认权限

**Q: 如何安全配置CORS？**
A:
1. 明确指定Origin，避免通配符
2. 谨慎使用`Allow-Credentials`
3. 限制允许的方法和头部
4. 设置合理的缓存时间

**Q: 为什么需要同源策略？**
A: 防止恶意网站读取其他网站的敏感数据，保护用户隐私和安全
