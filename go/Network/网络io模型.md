## 网络I/O模型

### 五种I/O模型对比

| I/O模型 | 阻塞性 | 编程难度 | 性能 | 适用场景 |
|---------|--------|----------|------|----------|
| **阻塞I/O** | 阻塞 | 简单 | 低 | 简单应用 |
| **非阻塞I/O** | 非阻塞 | 简单 | 中等 | 轮询场景 |
| **I/O多路复用** | 非阻塞 | 中等 | 高 | 高并发服务器 |
| **信号驱动I/O** | 非阻塞 | 复杂 | 高 | 实时系统 |
| **异步I/O** | 非阻塞 | 复杂 | 最高 | 高性能应用 |

### 详细说明

#### 1. 阻塞I/O (Blocking I/O)
- **原理**：进程发起I/O调用后被阻塞，直到数据准备好并复制到用户空间
- **特点**：简单直观，但效率低，一个连接一个线程
- **应用**：传统的socket编程

#### 2. 非阻塞I/O (Non-blocking I/O)
- **原理**：I/O调用立即返回，需要轮询检查数据是否准备好
- **特点**：避免阻塞，但需要不断轮询，浪费CPU
- **应用**：需要同时处理多个I/O的简单场景

#### 3. I/O多路复用 (I/O Multiplexing)
- **原理**：使用select/poll/epoll监控多个文件描述符
- **特点**：单线程处理多连接，避免线程切换开销
- **应用**：高并发Web服务器（Nginx、Redis）

**详细对比**：参见"socket 中 select 与 epoll.md"

#### 4. 信号驱动I/O (Signal-driven I/O)
- **原理**：注册信号处理函数，数据准备好时收到SIGIO信号
- **特点**：避免轮询，但信号处理复杂
- **应用**：实时性要求高的系统

#### 5. 异步I/O (Asynchronous I/O)
- **原理**：发起I/O请求后立即返回，操作完成后通过回调通知
- **特点**：真正的异步，性能最高，但编程复杂
- **应用**：高性能数据库、文件服务器

### 实际应用选择

**Web服务器**：
- **Apache**：多进程/多线程 + 阻塞I/O
- **Nginx**：单线程 + epoll多路复用
- **Node.js**：单线程 + 异步I/O

**数据库**：
- **MySQL**：多线程 + 阻塞I/O
- **Redis**：单线程 + epoll多路复用

### 面试要点

**Q: 为什么epoll比select性能好？**
A: 详见"socket 中 select 与 epoll.md"

**Q: 什么时候选择异步I/O？**
A: 高并发、高吞吐量场景，如文件服务器、数据库系统

**Q: 如何选择合适的I/O模型？**
A:
- **简单应用**：阻塞I/O
- **高并发服务器**：I/O多路复用（epoll）
- **高性能应用**：异步I/O