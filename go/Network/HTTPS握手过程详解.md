## HTTPS握手过程详解

### TLS握手流程

#### 完整握手过程（TLS 1.2）

```
客户端                                服务端
  |                                     |
  |----------- Client Hello ---------->|  1. 发送支持的加密套件
  |<---------- Server Hello -----------|  2. 选择加密套件
  |<---------- Certificate ------------|  3. 发送证书
  |<------ Server Key Exchange -------|  4. 发送公钥（可选）
  |<------ Certificate Request -------|  5. 请求客户端证书（可选）
  |<---------- Server Done ------------|  6. 服务端握手完成
  |                                     |
  |---------- Certificate ------------>|  7. 客户端证书（可选）
  |------ Client Key Exchange ------->|  8. 发送预主密钥
  |------ Certificate Verify -------->|  9. 证书验证（可选）
  |------ Change Cipher Spec -------->| 10. 切换到加密通信
  |----------- Finished -------------->| 11. 握手完成消息
  |                                     |
  |<----- Change Cipher Spec ----------| 12. 服务端切换加密
  |<---------- Finished --------------|  13. 服务端握手完成
  |                                     |
  |         加密的应用数据传输           |
```

### 详细步骤解析

#### 1. Client Hello
**客户端发送的信息**：
- **TLS版本**：支持的TLS版本（如TLS 1.2, 1.3）
- **随机数**：32字节随机数（用于生成密钥）
- **会话ID**：用于会话复用
- **加密套件列表**：支持的加密算法组合
- **压缩方法**：支持的压缩算法
- **扩展**：SNI、ALPN等扩展信息

#### 2. Server Hello
**服务端响应的信息**：
- **TLS版本**：选择的TLS版本
- **随机数**：服务端32字节随机数
- **会话ID**：确认或新建会话ID
- **加密套件**：从客户端列表中选择的套件
- **压缩方法**：选择的压缩方法

#### 3. Certificate
**服务端证书链**：
- **服务器证书**：包含服务器公钥
- **中间证书**：CA签发的中间证书
- **根证书**：客户端需要验证的根证书

#### 4. Server Key Exchange（可选）
**密钥交换参数**：
- **DHE/ECDHE**：临时密钥交换参数
- **签名**：使用服务器私钥签名，确保参数完整性

#### 5. Client Key Exchange
**预主密钥传输**：
- **RSA**：使用服务器公钥加密预主密钥
- **DHE/ECDHE**：发送客户端密钥交换参数

### 密钥生成过程

#### 主密钥生成
- 使用预主密钥、客户端和服务端随机数
- 通过PRF函数生成48字节主密钥

#### 会话密钥生成
- 从主密钥派生出加密密钥、MAC密钥、初始化向量
- 客户端和服务端各自使用不同的密钥

### 证书验证过程

#### 证书链验证
1. **证书格式验证**：检查证书格式是否正确
2. **有效期验证**：检查证书是否在有效期内
3. **域名验证**：检查证书域名是否匹配
4. **签名验证**：验证证书签名的有效性
5. **吊销检查**：检查证书是否被吊销（CRL/OCSP）

#### 信任链验证
- 验证证书链：服务器证书 ← 中间CA证书 ← 根CA证书
- 确保根证书在受信任的证书存储中

### TLS 1.3的改进

#### 简化的握手流程
```
客户端                    服务端
  |                        |
  |------ Client Hello ---->|  包含密钥共享
  |<----- Server Hello -----|  选择密钥共享
  |<----- Certificate ------|  
  |<----- Finished ---------|  
  |                        |
  |------ Finished ------->|  
  |                        |
  |    加密的应用数据       |
```

#### TLS 1.3优势
- **减少往返次数**：从2-RTT减少到1-RTT
- **更强的安全性**：移除不安全的加密算法
- **0-RTT恢复**：会话恢复时可以0-RTT
- **前向安全性**：强制使用ECDHE

### 性能优化

#### 会话复用
**Session ID复用**：
- 服务端保存会话状态
- 客户端发送Session ID
- 跳过完整握手过程

**Session Ticket复用**：
- 服务端将会话状态加密发给客户端
- 客户端下次连接时发送Ticket
- 无需服务端存储状态

#### OCSP Stapling
- 服务端预先获取OCSP响应
- 在握手时一并发送给客户端
- 减少客户端的OCSP查询

### 常见问题与解决

#### 握手失败原因
1. **证书问题**：过期、域名不匹配、自签名
2. **加密套件不匹配**：客户端和服务端无共同支持的套件
3. **协议版本不兼容**：TLS版本不匹配
4. **网络问题**：超时、丢包

#### 性能问题
1. **握手延迟高**：使用会话复用、OCSP Stapling
2. **CPU占用高**：使用硬件加速、优化加密算法
3. **内存占用大**：合理设置会话缓存大小

### 面试要点

**Q: HTTPS握手过程中的关键步骤？**
A: 
1. 协商加密套件和TLS版本
2. 服务端发送证书，客户端验证
3. 密钥交换，生成会话密钥
4. 切换到加密通信

**Q: 如何优化HTTPS握手性能？**
A:
1. 会话复用（Session ID/Ticket）
2. OCSP Stapling减少证书验证延迟
3. 使用TLS 1.3减少握手往返
4. HTTP/2多路复用减少连接数

**Q: TLS 1.2和TLS 1.3的主要区别？**
A:
1. 握手往返次数：2-RTT vs 1-RTT
2. 安全性：移除不安全算法，强制前向安全
3. 性能：支持0-RTT会话恢复

**Q: 证书验证包括哪些步骤？**
A: 格式验证、有效期验证、域名验证、签名验证、吊销检查、信任链验证
